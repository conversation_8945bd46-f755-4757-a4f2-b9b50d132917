// SPDX-License-Identifier: MIT
pragma solidity 0.8.24;

import "./TestContracts/DevTestSetup.sol";

contract ZapperVulnerabilityPOC is DevTestSetup {

    // Test addresses
    address alice = makeAddr("alice");
    address bob = makeAddr("bob");
    address attacker = makeAddr("attacker");

    // Test variables
    uint256 aliceTroveId;
    uint256 bobTroveId;
    uint256 attackerTroveId;

    function setUp() public override {
        super.setUp();

        // Give test accounts some ETH and tokens
        vm.deal(alice, 1000 ether);
        vm.deal(bob, 1000 ether);
        vm.deal(attacker, 1000 ether);

        // Setup initial troves to establish system state
        _setupInitialTroves();
    }

    function _setupInitialTroves() internal {
        // Alice opens a trove with high ICR
        vm.startPrank(alice);
        aliceTroveId = borrowerOperations.openTrove(
            alice,
            0, // ownerIndex
            100 ether, // collAmount
            2000 ether, // boldAmount
            0, // upperHint
            0, // lowerHint
            0.05 ether, // annualInterestRate (5%)
            type(uint256).max, // maxUpfrontFee
            address(0), // addManager
            address(0), // removeManager
            address(0) // receiver
        );
        vm.stopPrank();

        // Bob opens a trove
        vm.startPrank(bob);
        bobTroveId = borrowerOperations.openTrove(
            bob,
            0, // ownerIndex
            50 ether, // collAmount
            1000 ether, // boldAmount
            0, // upperHint
            0, // lowerHint
            0.05 ether, // annualInterestRate (5%)
            type(uint256).max, // maxUpfrontFee
            address(0), // addManager
            address(0), // removeManager
            address(0) // receiver
        );
        vm.stopPrank();
    }

    function testVulnerability_ZapperFailsWhenBorrowerOperationsReverts() public {
        console.log("=== POC: Zapper Vulnerability - BorrowerOperations Revert Scenario ===");

        // Step 1: Setup attacker trove with Zapper as receiver
        _setupAttackerTroveWithZapper();

        // Step 2: Force system below Critical Threshold
        _forceSystemBelowCriticalThreshold();

        // Step 3: Attempt withdrawal through Zapper - should fail
        _attemptZapperWithdrawal();

        console.log(" Vulnerability confirmed: Zapper fails when BorrowerOperations reverts");
    }

    function _setupAttackerTroveWithZapper() internal {
        console.log("Setting up attacker trove with Zapper as receiver...");

        vm.startPrank(attacker);

        // Open trove with Zapper as remove manager and receiver
        attackerTroveId = borrowerOperations.openTrove(
            attacker,
            0, // ownerIndex
            20 ether, // collAmount
            400 ether, // boldAmount
            0, // upperHint
            0, // lowerHint
            0.05 ether, // annualInterestRate (5%)
            type(uint256).max, // maxUpfrontFee
            address(0), // addManager
            address(gasCompZapper), // removeManager - Zapper can withdraw
            address(gasCompZapper) // receiver - Zapper receives withdrawn collateral
        );

        console.log("Attacker trove ID:", attackerTroveId);
        console.log("Attacker trove collateral:", troveManager.getTroveEntireColl(attackerTroveId));

        vm.stopPrank();
    }

    function _forceSystemBelowCriticalThreshold() internal {
        console.log("Forcing system below Critical Threshold...");

        uint256 currentPrice = priceFeed.getPrice();
        console.log("Current price:", currentPrice);

        // Get current TCR
        uint256 currentTCR = troveManager.getTCR(currentPrice);
        console.log("Current TCR:", currentTCR);
        console.log("CCR (Critical Threshold):", CCR);

        // Calculate price needed to push TCR below CCR
        // We need to drop price significantly to trigger Critical Threshold
        uint256 newPrice = currentPrice * 60 / 100; // 40% price drop

        priceFeed.setPrice(newPrice);

        uint256 newTCR = troveManager.getTCR(newPrice);
        console.log("New price:", newPrice);
        console.log("New TCR:", newTCR);
        console.log("Is below Critical Threshold:", newTCR < CCR);

        assertTrue(newTCR < CCR, "System should be below Critical Threshold");
    }

    function _attemptZapperWithdrawal() internal {
        console.log("Attempting withdrawal through Zapper...");

        uint256 withdrawAmount = 1 ether;
        uint256 zapperBalanceBefore = collToken.balanceOf(address(gasCompZapper));
        uint256 attackerBalanceBefore = collToken.balanceOf(attacker);

        console.log("Zapper balance before:", zapperBalanceBefore);
        console.log("Attacker balance before:", attackerBalanceBefore);
        console.log("Attempting to withdraw:", withdrawAmount);

        vm.startPrank(attacker);

        // This should fail because:
        // 1. BorrowerOperations.withdrawColl will revert due to Critical Threshold restrictions
        // 2. But Zapper doesn't handle this revert properly
        // 3. If BorrowerOperations doesn't revert but withdraws less, Zapper will try to send more than it has

        vm.expectRevert(); // We expect this to revert
        gasCompZapper.withdrawColl(attackerTroveId, withdrawAmount);

        vm.stopPrank();

        console.log(" Withdrawal correctly reverted due to Critical Threshold restrictions");
        console.log(" This demonstrates the vulnerability: Zapper cannot handle BorrowerOperations failures");
    }

    function testDirectBorrowerOperationsWithdrawal() public {
        console.log("=== Testing Direct BorrowerOperations Withdrawal for Comparison ===");

        _setupAttackerTroveWithZapper();
        _forceSystemBelowCriticalThreshold();

        uint256 withdrawAmount = 1 ether;

        vm.startPrank(attacker);

        // Direct call to BorrowerOperations should also revert
        vm.expectRevert();
        borrowerOperations.withdrawColl(attackerTroveId, withdrawAmount);

        vm.stopPrank();

        console.log("Direct BorrowerOperations call also reverts as expected");
        console.log(" This confirms the system is working correctly at the BorrowerOperations level");
    }

    function testSystemRecoveryAfterPriceIncrease() public {
        console.log("=== Testing System Recovery After Price Increase ===");

        _setupAttackerTroveWithZapper();
        _forceSystemBelowCriticalThreshold();

        // Now increase price to bring system back above Critical Threshold
        uint256 recoveryPrice = priceFeed.getPrice() * 200 / 100; // Double the price

        vm.prank(owner);
        priceFeed.setPrice(recoveryPrice);

        uint256 newTCR = troveManager.getTCR(recoveryPrice);
        console.log("Recovery price:", recoveryPrice);
        console.log("Recovery TCR:", newTCR);
        console.log("Is above Critical Threshold:", newTCR >= CCR);

        assertTrue(newTCR >= CCR, "System should be above Critical Threshold");

        // Now withdrawal should work
        uint256 withdrawAmount = 1 ether;

        vm.startPrank(attacker);

        // This should now succeed
        gasCompZapper.withdrawColl(attackerTroveId, withdrawAmount);

        vm.stopPrank();

        console.log(" Withdrawal succeeded after system recovery");
        console.log(" This shows the vulnerability is condition-dependent");
    }
}