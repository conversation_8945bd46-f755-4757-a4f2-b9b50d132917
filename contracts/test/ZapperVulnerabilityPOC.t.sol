// SPDX-License-Identifier: MIT
pragma solidity 0.8.24;

import "./TestContracts/DevTestSetup.sol";

contract ZapperVulnerabilityPOC is DevTestSetup {

    // Test variables
    uint256 aliceTroveId;
    uint256 bobTroveId;
    uint256 attackerTroveId;

    function setUp() public override {
        super.setUp();

        // Setup initial troves to establish system state using existing accounts
        _setupInitialTroves();
    }

    function _setupInitialTroves() internal {
        // Use existing accounts A, B from DevTestSetup
        // Alice (A) opens a trove with high ICR - using MIN_DEBT (2000e18) as minimum
        aliceTroveId = openTroveNoHints100pct(A, 100 ether, 5000 ether, 0.05 ether);

        // Bob (B) opens a trove - using above MIN_DEBT
        bobTroveId = openTroveNoHints100pct(B, 50 ether, 3000 ether, 0.05 ether);
    }

    function testVulnerability_ZapperFailsWhenBorrowerOperationsReverts() public {
        console.log("=== POC: Zapper Vulnerability - BorrowerOperations Revert <PERSON>rio ===");

        // Step 1: Setup attacker trove with <PERSON><PERSON><PERSON> as receiver
        _setupAttackerTroveWithZapper();

        // Step 2: Force system below Critical Threshold
        _forceSystemBelowCriticalThreshold();

        // Step 3: Attempt withdrawal through Zapper - should fail
        _attemptZapperWithdrawal();

        console.log(" Vulnerability confirmed: Zapper fails when BorrowerOperations reverts");
    }

    function _setupAttackerTroveWithZapper() internal {
        console.log("Setting up attacker trove with Zapper as receiver...");

        // Use account C as attacker and open trove with Zapper as remove manager and receiver
        vm.startPrank(C);
        attackerTroveId = borrowerOperations.openTrove(
            C,
            0, // ownerIndex
            20 ether, // collAmount
            400 ether, // boldAmount
            0, // upperHint
            0, // lowerHint
            0.05 ether, // annualInterestRate (5%)
            type(uint256).max, // maxUpfrontFee
            address(0), // addManager
            address(gasCompZapper), // removeManager - Zapper can withdraw
            address(gasCompZapper) // receiver - Zapper receives withdrawn collateral
        );
        vm.stopPrank();

        console.log("Attacker trove ID:", attackerTroveId);
        console.log("Attacker trove collateral:", troveManager.getTroveEntireColl(attackerTroveId));
    }

    function _forceSystemBelowCriticalThreshold() internal {
        console.log("Forcing system below Critical Threshold...");

        uint256 currentPrice = priceFeed.getPrice();
        console.log("Current price:", currentPrice);

        // Get current TCR
        uint256 currentTCR = troveManager.getTCR(currentPrice);
        console.log("Current TCR:", currentTCR);
        console.log("CCR (Critical Threshold):", CCR);

        // Calculate price needed to push TCR below CCR
        // We need to drop price significantly to trigger Critical Threshold
        uint256 newPrice = currentPrice * 60 / 100; // 40% price drop

        priceFeed.setPrice(newPrice);

        uint256 newTCR = troveManager.getTCR(newPrice);
        console.log("New price:", newPrice);
        console.log("New TCR:", newTCR);
        console.log("Is below Critical Threshold:", newTCR < CCR);

        assertTrue(newTCR < CCR, "System should be below Critical Threshold");
    }

    function _attemptZapperWithdrawal() internal {
        console.log("Attempting withdrawal through Zapper...");

        uint256 withdrawAmount = 1 ether;
        uint256 zapperBalanceBefore = collToken.balanceOf(address(gasCompZapper));
        uint256 attackerBalanceBefore = collToken.balanceOf(C);

        console.log("Zapper balance before:", zapperBalanceBefore);
        console.log("Attacker balance before:", attackerBalanceBefore);
        console.log("Attempting to withdraw:", withdrawAmount);

        vm.startPrank(C); // Use account C as attacker

        // This should fail because:
        // 1. BorrowerOperations.withdrawColl will revert due to Critical Threshold restrictions
        // 2. But Zapper doesn't handle this revert properly
        // 3. If BorrowerOperations doesn't revert but withdraws less, Zapper will try to send more than it has

        vm.expectRevert(); // We expect this to revert
        gasCompZapper.withdrawColl(attackerTroveId, withdrawAmount);

        vm.stopPrank();

        console.log(" Withdrawal correctly reverted due to Critical Threshold restrictions");
        console.log(" This demonstrates the vulnerability: Zapper cannot handle BorrowerOperations failures");
    }

    function testDirectBorrowerOperationsWithdrawal() public {
        console.log("=== Testing Direct BorrowerOperations Withdrawal for Comparison ===");

        _setupAttackerTroveWithZapper();
        _forceSystemBelowCriticalThreshold();

        uint256 withdrawAmount = 1 ether;

        vm.startPrank(C); // Use account C as attacker

        // Direct call to BorrowerOperations should also revert
        vm.expectRevert();
        borrowerOperations.withdrawColl(attackerTroveId, withdrawAmount);

        vm.stopPrank();

        console.log("Direct BorrowerOperations call also reverts as expected");
        console.log(" This confirms the system is working correctly at the BorrowerOperations level");
    }

    function testSystemRecoveryAfterPriceIncrease() public {
        console.log("=== Testing System Recovery After Price Increase ===");

        _setupAttackerTroveWithZapper();
        _forceSystemBelowCriticalThreshold();

        // Now increase price to bring system back above Critical Threshold
        uint256 recoveryPrice = priceFeed.getPrice() * 200 / 100; // Double the price

        priceFeed.setPrice(recoveryPrice);

        uint256 newTCR = troveManager.getTCR(recoveryPrice);
        console.log("Recovery price:", recoveryPrice);
        console.log("Recovery TCR:", newTCR);
        console.log("Is above Critical Threshold:", newTCR >= CCR);

        assertTrue(newTCR >= CCR, "System should be above Critical Threshold");

        // Now withdrawal should work
        uint256 withdrawAmount = 1 ether;

        vm.startPrank(C); // Use account C as attacker

        // This should now succeed
        gasCompZapper.withdrawColl(attackerTroveId, withdrawAmount);

        vm.stopPrank();

        console.log(" Withdrawal succeeded after system recovery");
        console.log(" This shows the vulnerability is condition-dependent");
    }
}