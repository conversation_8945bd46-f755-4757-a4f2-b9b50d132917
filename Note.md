 The root cause is a balance tracking mismatch between what <PERSON>rrower<PERSON>per<PERSON> actually withdraws versus what the <PERSON><PERSON>per assumes was withdrawn.

The Problem Flow
Zapper requests withdrawal: The <PERSON>apper calls borrowerOperations.withdrawColl(_troveId, _amount) requesting a specific _amount

BorrowerOperations may withdraw less: BorrowerOperations has internal constraints that can reduce the actual withdrawal amount below the requested amount. These constraints include:

Critical Collateral Ratio (CCR) restrictions when the system is below the critical threshold BorrowerOperations.sol:1435-1441
Minimum Collateral Ratio (MCR) requirements that prevent withdrawals that would make the trove undercollateralized BorrowerOperations.sol:1444-1448
Validation checks that can reject withdrawals entirely BorrowerOperations.sol:1480-1483
<PERSON><PERSON><PERSON> blindly sends requested amount: After the BorrowerOperations call, the Zapper immediately attempts to transfer the originally requested _amount to the receiver, without checking how much was actually withdrawn

Why This Fails
The critical flaw is that BorrowerOperations doesn't return the actual withdrawal amount, and the Zapper doesn't track its collateral balance before/after the withdrawal. When BorrowerOperations withdraws less than requested (or nothing at all due to constraints), the Zapper still tries to send the full requested amount, causing the safeTransfer to revert due to insufficient balance.

Evidence from Tests
The test suite confirms this behavior - when the system is below the Critical Threshold, withdrawColl() calls to BorrowerOperations can revert entirely BorrowerOperationsTest.js:549-556 , but the Zapper code doesn't handle this scenario properly.

Notes

This is fundamentally a design issue where the Zapper assumes BorrowerOperations will always withdraw the exact requested amount, but BorrowerOperations has safety mechanisms that can reduce or prevent withdrawals entirely. The fix would require the Zapper to either check its balance changes or have BorrowerOperations return the actual withdrawal amount.